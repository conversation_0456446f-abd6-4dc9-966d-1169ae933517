#!/usr/bin/env python
# -*- coding: utf-8 -*-

import json
import logging
import random
import datetime
import sys
import os
import time
import numpy as np
from typing import Dict, Any, List, Tuple
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.database import ES_CLIENT
from src.modules.ai_service import AIService
from src.modules.belief_module import BeliefModule
from src.modules.agent_module import AgentModule
from src.modules.memory_module import MemoryModule
from lib.belief_ai_data import get_emotional_disposition, get_embedding, is_data_available

# 导入配置
from config.config import Config
import config.parallel_init_config as parallel_config

# 使用配置文件中的参数
sim_config = Config.get_simulation_config()
NUM_USERS = sim_config['num_users']
MAX_WORKERS = parallel_config.get_effective_max_workers()
BATCH_SIZE = parallel_config.get_effective_batch_size()

# 配置日志记录
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("TraitBasedDatabaseInitializer")

# 索引名称
AGENTS_INDEX = "agents"
BELIEFS_INDEX = "beliefs"
POSTS_INDEX = "posts"
COMMENTS_INDEX = "comments"
MEMORIES_INDEX = "memories"

class TraitBasedDatabaseInitializer:
    """
    基于特质的数据库初始化器，根据用户的认知特质来决定其社交影响力，
    不再区分知识分子和普通群众。
    """
    def __init__(self):
        self.ai_service = AIService(
            api_key="sk-mT8uDsuxCwxMPUlwdodDOcdjzJjOYFBQQWw17LLLDPXyqVH0",
            base_url="http://47.102.193.166:8060",
            embedding_url="http://10.201.64.106:30000/v1",
            reranker_url="http://10.201.64.106:30001/v1"
        )
        self.agent_module = AgentModule()
        self.memory_module = MemoryModule(self.ai_service)
        self.belief_module = BeliefModule(self.agent_module, self.memory_module, self.ai_service)
        
        topic_paths = Config.get_topic_data_paths()
        self.beliefs_data = self._load_json_data(topic_paths['belief_file'])
        self.posts_data = self._load_json_data(topic_paths['post_file'])
        self.search_data = self._load_json_data(topic_paths['search_file'])
        
        # 创建用户ID和人格特质
        self.users = self._generate_users_by_traits(NUM_USERS)
        
        # 线程安全的数据存储
        self._lock = threading.Lock()
        self._pending_beliefs = []  # 待上传的信念数据
        self._pending_relations = []  # 待上传的关系数据
        
    def _load_json_data(self, file_path: str) -> List[Dict]:
        """加载JSON数据文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                return json.load(file)
        except Exception as e:
            logger.error(f"加载{file_path}失败: {e}")
            return []

    def _generate_trait_values(self, num_users: int) -> List[Dict[str, float]]:
        """
        生成用户特质值，使用指数分布确保高怀疑分数、高求证阈值、低情绪波动的用户较少
        """
        traits_list = []
        
        for _ in range(num_users):
            # 使用更温和的分布生成特质值
            # 怀疑分数：大部分用户中等偏低，少数用户较高
            skepticism_raw = np.random.exponential(scale=0.4)
            skepticism_score = min(0.8, max(0.2, 0.3 + skepticism_raw * 0.4))

            # 求证阈值：与怀疑分数正相关，但有一定随机性
            verification_base = skepticism_score + np.random.normal(0, 0.15)
            verification_threshold = min(0.8, max(0.2, verification_base))

            # 情绪波动：与怀疑分数负相关，但不要太极端
            emotional_base = 0.6 - skepticism_score * 0.3 + np.random.normal(0, 0.15)
            emotional_volatility = min(0.8, max(0.2, emotional_base))
            
            # 计算综合特质分数（用于后续计算影响力）
            trait_score = (skepticism_score * 0.4 + verification_threshold * 0.4 + 
                          (1 - emotional_volatility) * 0.2)
            
            traits_list.append({
                "skepticism_score": round(skepticism_score, 2),
                "verification_threshold": round(verification_threshold, 2),
                "emotional_volatility": round(emotional_volatility, 2),
                "trait_score": round(trait_score, 2)  # 综合特质分数
            })
        
        # 按特质分数排序，便于后续分析
        traits_list.sort(key=lambda x: x["trait_score"], reverse=True)
        
        # 记录特质分布统计
        trait_scores = [t["trait_score"] for t in traits_list]
        logger.info(f"特质分数分布 - 最高: {max(trait_scores):.2f}, 最低: {min(trait_scores):.2f}, "
                   f"平均: {np.mean(trait_scores):.2f}, 中位数: {np.median(trait_scores):.2f}")
        
        # 统计高特质用户比例
        high_trait_users = sum(1 for score in trait_scores if score > 0.7)
        logger.info(f"高特质用户(>0.7): {high_trait_users} 个 ({high_trait_users/len(trait_scores)*100:.1f}%)")
        
        return traits_list

    def _generate_users_by_traits(self, num_users: int) -> Dict[str, Dict[str, Any]]:
        """基于特质生成用户，不再区分知识分子和普通群众"""
        users = {}
        
        # 生成特质值
        traits_list = self._generate_trait_values(num_users)
        
        for i in range(1, num_users + 1):
            user_id = f"user_{i}"
            
            # 随机生成大五人格特质
            ocean = {
                "O": round(random.uniform(0.2, 0.9), 2),  # 开放性
                "C": round(random.uniform(0.2, 0.9), 2),  # 尽责性 
                "E": round(random.uniform(0.2, 0.9), 2),  # 外向性
                "A": round(random.uniform(0.2, 0.9), 2),  # 宜人性
                "N": round(random.uniform(0.2, 0.9), 2)   # 神经质
            }
            
            # 使用预生成的认知特质
            cognitive_traits = traits_list[i-1]
            
            users[user_id] = {
                "user_id": user_id,
                "ocean_personality": ocean,
                "cognitive_traits": cognitive_traits,
                "followed_posts": [],
                "followed_users": [],
                "followers": [],
                "posts": [],
                "comments": []
            }
            
        return users

    def initialize_database(self):
        """初始化数据库，添加用户、信念和帖子"""
        logger.info("开始基于特质的数据库初始化...")
        
        # 清空并重建索引
        self._reset_indices()
        
        # 添加用户
        self._add_users()
        
        # 建立基于特质的用户关注关系
        self._build_trait_based_relationships()
        
        # 并行添加用户信念
        self._add_beliefs_parallel()
        
        # 添加帖子
        self._add_posts()
        
        # 打印用户信念图
        self._print_belief_graphs_for_all_users()
        
        logger.info("基于特质的数据库初始化完成!")

    def _reset_indices(self):
        """重置所有索引"""
        indices = [AGENTS_INDEX, BELIEFS_INDEX, POSTS_INDEX, COMMENTS_INDEX, MEMORIES_INDEX]
        
        for index in indices:
            if ES_CLIENT.indices.exists(index=index):
                logger.info(f"删除索引: {index}")
                ES_CLIENT.indices.delete(index=index)
                
            logger.info(f"创建索引: {index}")
            
            # 根据索引类型设置不同的映射
            if index == BELIEFS_INDEX:
                mapping = {
                    "properties": {
                        "embedding": {"type": "dense_vector", "dims": 4096},
                        "relation_edges": {"type": "nested"}
                    }
                }
                ES_CLIENT.indices.create(index=index, mappings=mapping)
            elif index == MEMORIES_INDEX:
                mapping = {
                    "properties": {
                        "user_id": {"type": "keyword"},
                        "type": {"type": "keyword"},
                        "embedding": {"type": "dense_vector", "dims": 4096},
                        "timestamp": {"type": "date"}
                    }
                }
                ES_CLIENT.indices.create(index=index, mappings=mapping)
            else:
                ES_CLIENT.indices.create(index=index)

    def _add_users(self):
        """将生成的用户批量添加到数据库中"""
        logger.info(f"批量添加 {len(self.users)} 个用户到数据库...")

        # 准备批量操作
        bulk_operations = []

        for user_id, user_data in self.users.items():
            # 添加索引操作到批量操作列表
            bulk_operations.append({
                "index": {
                    "_index": AGENTS_INDEX,
                    "_id": user_id
                }
            })
            bulk_operations.append(user_data)

        # 执行批量操作
        try:
            if bulk_operations:
                logger.info(f"开始批量插入 {len(self.users)} 个用户...")
                response = ES_CLIENT.bulk(body=bulk_operations)

                # 检查批量操作结果
                errors = []
                successful_count = 0

                for item in response["items"]:
                    if "index" in item:
                        if item["index"]["status"] in [200, 201]:
                            successful_count += 1
                        else:
                            errors.append(item["index"])

                logger.info(f"用户批量添加完成 - 成功: {successful_count} 个, 失败: {len(errors)} 个")

                if errors:
                    logger.warning(f"有 {len(errors)} 个用户添加失败")
                    for error in errors[:5]:  # 只显示前5个错误
                        logger.warning(f"错误详情: {error}")
            else:
                logger.warning("没有用户需要添加")

        except Exception as e:
            logger.error(f"批量添加用户失败: {e}")
            # 如果批量操作失败，回退到逐个添加
            logger.info("回退到逐个添加用户...")
            successful_count = 0
            for user_id, user_data in self.users.items():
                try:
                    ES_CLIENT.index(index=AGENTS_INDEX, id=user_id, document=user_data)
                    successful_count += 1
                except Exception as individual_error:
                    logger.error(f"添加用户 {user_id} 失败: {individual_error}")

            logger.info(f"逐个添加完成 - 成功: {successful_count} 个")

    def _build_trait_based_relationships(self):
        """基于用户特质建立关注关系"""
        logger.info("开始建立基于特质的用户关注关系...")

        # 按特质分数对用户进行分组
        users_by_trait = []
        for user_id, user_data in self.users.items():
            trait_score = user_data["cognitive_traits"]["trait_score"]
            users_by_trait.append((user_id, trait_score, user_data))

        # 按特质分数排序
        users_by_trait.sort(key=lambda x: x[1], reverse=True)

        logger.info(f"用户特质分数范围: {users_by_trait[0][1]:.2f} - {users_by_trait[-1][1]:.2f}")

        # 建立关注关系
        self._build_trait_based_follow_relationships(users_by_trait)

        logger.info("基于特质的用户关注关系建立完成")

    def _build_trait_based_follow_relationships(self, users_by_trait: List[Tuple[str, float, Dict]]):
        """基于特质分数建立关注关系，高特质用户获得更多关注"""
        logger.info("建立基于特质的关注关系...")

        if len(users_by_trait) < 2:
            logger.warning("用户数量不足，无法建立关注关系")
            return

        all_follow_relationships = []

        # 为每个用户计算其应该获得的粉丝数量
        for user_id, trait_score, _ in users_by_trait:
            # 基于特质分数计算粉丝数量（连续分布）
            # 使用指数函数：特质分数越高，粉丝数量指数增长
            base_followers = len(users_by_trait) * 0.05  # 基础粉丝数（降低基础值）
            trait_multiplier = np.exp(trait_score * 2.5)  # 特质乘数，指数增长
            max_followers = len(users_by_trait) * 0.7   # 最大粉丝数限制

            target_followers = min(max_followers, base_followers * trait_multiplier)
            target_followers = max(1, int(target_followers))  # 至少1个粉丝

            # 可以成为粉丝的用户（排除自己）
            potential_followers = [uid for uid, _, _ in users_by_trait if uid != user_id]

            if not potential_followers:
                continue

            # 确保不超过可用用户数量
            actual_follower_count = min(target_followers, len(potential_followers))

            if actual_follower_count > 0:
                # 选择粉丝时，给予低特质用户更高的概率关注高特质用户
                follower_weights = []
                for follower_id, follower_trait_score, _ in users_by_trait:
                    if follower_id == user_id:
                        continue
                    # 低特质用户更容易关注高特质用户
                    weight = 1.0 / (follower_trait_score + 0.1)  # 避免除零
                    follower_weights.append(weight)

                # 根据权重选择粉丝
                if len(potential_followers) == len(follower_weights):
                    followers = np.random.choice(
                        potential_followers,
                        size=actual_follower_count,
                        replace=False,
                        p=np.array(follower_weights) / sum(follower_weights)
                    )
                else:
                    # 如果权重计算有问题，使用随机选择
                    followers = random.sample(potential_followers, actual_follower_count)

                # 添加到批量关注列表
                for follower_id in followers:
                    all_follow_relationships.append((follower_id, user_id))

                logger.debug(f"用户 {user_id} (特质分数: {trait_score:.2f}) 将获得 {len(followers)} 个粉丝")

        # 批量执行关注操作
        if all_follow_relationships:
            logger.info(f"开始批量建立 {len(all_follow_relationships)} 个基于特质的关注关系")
            result = self.agent_module.batch_update_user_follow(all_follow_relationships, follow=True)

            logger.info(f"特质关注关系建立完成 - 成功: {result['successful_count']}, 失败: {result['failed_count']}")

            if result['failed_count'] > 0:
                logger.warning(f"有 {result['failed_count']} 个关系建立失败")
                for failed_rel in result.get('failed_relationships', []):
                    if len(failed_rel) >= 3:
                        logger.debug(f"失败关系: {failed_rel[0]} -> {failed_rel[1]}, 原因: {failed_rel[2]}")
        else:
            logger.info("没有需要建立的关注关系")

    def _add_beliefs_parallel(self):
        """并行为每个用户添加信念，批量上传到数据库"""
        logger.info("开始并行为用户添加信念...")

        # 检查是否有预处理的AI数据
        if is_data_available():
            logger.info("✓ 检测到预处理的AI数据，将使用真实AI生成的数据")
        else:
            logger.warning("⚠ 未检测到预处理的AI数据")
            logger.warning("将使用AI服务实时生成数据，这会非常慢（预计需要2-3小时）")
            logger.info("强烈建议先运行: python scripts/preprocess_beliefs.py")

            # 询问用户是否继续
            import time
            logger.warning("如果您想继续使用实时AI服务，请等待10秒...")
            logger.warning("或者按 Ctrl+C 取消，先运行预处理脚本")
            try:
                time.sleep(5)
                logger.info("继续使用实时AI服务...")
            except KeyboardInterrupt:
                logger.info("用户取消操作，请先运行预处理脚本")
                return

        # 将用户分组进行并行处理
        user_items = list(self.users.items())
        total_users = len(user_items)

        logger.info(f"开始并行处理 {total_users} 个用户的信念图构建，使用 {MAX_WORKERS} 个线程")

        # 使用线程池并行处理用户信念
        with ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
            # 提交所有用户的信念构建任务
            future_to_user = {
                executor.submit(self._process_user_beliefs, user_id, user_data): user_id
                for user_id, user_data in user_items
            }

            processed_count = 0

            # 收集结果并定期批量上传
            for future in as_completed(future_to_user):
                user_id = future_to_user[future]
                try:
                    user_beliefs_data = future.result()
                    processed_count += 1

                    # 将用户的信念数据添加到待上传队列
                    with self._lock:
                        self._pending_beliefs.extend(user_beliefs_data['beliefs'])
                        self._pending_relations.extend(user_beliefs_data['relations'])

                    # 每处理完一定数量的用户或达到批次大小时，执行批量上传
                    if processed_count % BATCH_SIZE == 0 or processed_count == total_users:
                        self._batch_upload_beliefs()

                    # 显示进度
                    if processed_count % 50 == 0 or processed_count == total_users:
                        logger.info(f"信念图构建进度: {processed_count}/{total_users} ({processed_count/total_users*100:.1f}%)")

                except Exception as e:
                    logger.error(f"处理用户 {user_id} 的信念时发生错误: {e}")

        # 确保所有剩余的数据都被上传
        self._batch_upload_beliefs()

        logger.info("并行信念添加完成!")

    def _process_user_beliefs(self, user_id: str, user_data: Dict[str, Any]) -> Dict[str, List]:
        """为单个用户处理信念图构建，完全基于特质分数的连续分布"""
        user_beliefs = []
        user_relations = []

        # 根据用户特质分数确定该用户可能持有的信念数量（连续分布）
        trait_score = user_data["cognitive_traits"]["trait_score"]

        # 基于特质分数连续计算信念数量：2-8个信念（减少信念数量）
        min_beliefs = 2
        max_beliefs = 8
        num_beliefs = int(min_beliefs + (max_beliefs - min_beliefs) * trait_score)
        num_beliefs = max(min_beliefs, min(max_beliefs, num_beliefs))

        # 如果信念数量为0，跳过该用户
        if num_beliefs == 0:
            logger.debug(f"用户 {user_id} 不添加任何信念")
            return {"beliefs": [], "relations": []}

        # 随机选择信念（直接从beliefs_data中随机选择）
        chosen_beliefs = random.sample(self.beliefs_data, min(num_beliefs, len(self.beliefs_data)))

        belief_id_counter = int(time.time() * 1000000) + hash(user_id) % 1000000  # 生成唯一ID
        user_belief_ids = []

        # 为用户创建信念
        for belief in chosen_beliefs:
            if not belief:
                continue

            belief_id = f"belief_{belief_id_counter}"
            belief_id_counter += 1
            user_belief_ids.append(belief_id)

            # 基于特质分数连续计算信念来源权重（平滑的极化分布）
            # 高特质分数 -> 主要从proposition，oppositional_view应该<10%
            # 低特质分数 -> 主要从oppositional_view，proposition应该<10%
            # 低特质用户vernacular_saying应该≤15%

            # proposition权重：特质分数越高，权重越大，低特质用户极少
            proposition_weight = 0.01 + 0.79 * (trait_score ** 1.5)

            # oppositional_view权重：平滑递减，保持中低特质用户较高比例
            # 使用更强的递减函数让高特质用户的oppositional_view<10%
            oppositional_weight = 0.8 * ((1 - trait_score) ** 1.5) + 0.02

            # vernacular_saying权重：低特质用户较少(≤15%)，中等特质用户适中
            # 使用更平滑的增长函数，确保低特质用户≤15%
            if trait_score <= 0.3:
                # 低特质用户：vernacular_saying保持较低
                vernacular_weight = 0.07 + 0.06 * (trait_score / 0.3)
            else:
                # 中高特质用户：vernacular_saying适度增长
                vernacular_weight = 0.13 + 0.15 * ((trait_score - 0.3) / 0.7)

            # 归一化权重
            total_weight = proposition_weight + oppositional_weight + vernacular_weight
            source_weights = {
                "proposition": proposition_weight / total_weight,
                "oppositional_view": oppositional_weight / total_weight,
                "vernacular_saying": vernacular_weight / total_weight
            }

            # 根据权重随机选择信念来源
            sources = list(source_weights.keys())
            weights = list(source_weights.values())
            belief_source = random.choices(sources, weights=weights, k=1)[0]

            # 获取对应来源的信念内容
            belief_summary = belief[belief_source]

            # 基于特质分数和信念来源连续调整真实度和置信度（更温和的范围）
            base_veracity = 0.2 + 0.4 * trait_score  # 基础真实度：0.4-0.8
            base_confidence = 0.2 + 0.3 * trait_score  # 基础置信度：0.4-0.7

            # 根据信念来源调整
            if belief_source == "proposition":
                # proposition来源的信念真实度和置信度稍高
                veracity_score = min(0.9, base_veracity + 0.1)
                confidence = min(0.8, base_confidence + 0.1)
            elif belief_source == "oppositional_view":
                # oppositional_view来源的信念真实度稍低
                veracity_score = max(0.3, base_veracity - 0.1)
                confidence = base_confidence
            else:  # vernacular_saying
                # vernacular_saying来源的信念真实度和置信度稍低
                veracity_score = max(0.3, base_veracity - 0.05)
                confidence = max(0.3, base_confidence - 0.05)

            # 添加适度的随机性
            veracity_score += random.uniform(-0.05, 0.05)
            confidence += random.uniform(-0.05, 0.05)

            # 确保在合理范围内
            veracity_score = max(0.3, min(0.9, veracity_score))
            confidence = max(0.3, min(0.8, confidence))

            # 获取AI数据
            try:
                if is_data_available():
                    # 使用预处理的AI数据
                    emotional_disposition = get_emotional_disposition(belief["id"], belief_source)
                    embedding = get_embedding(belief["id"], belief_source)
                else:
                    # 使用AI服务实时生成
                    emotional_disposition, embedding = self._get_ai_data_for_belief(belief_summary)
            except Exception as e:
                logger.error(f"获取信念 {belief_id} 的AI数据失败: {e}")
                # 使用默认值
                emotional_disposition = {
                    "joy": 0.5, "sadness": 0.5, "anger": 0.5, "fear": 0.5,
                    "surprise": 0.5, "trust": 0.5, "disgust": 0.5, "anticipation": 0.5
                }
                embedding = [0.0] * 4096  # 默认嵌入向量

            # 创建符合BeliefModule期望结构的信念文档
            belief_doc = {
                "node_id": belief_id,
                "owner_user_id": user_id,
                "belief_summary": belief_summary,
                "embedding": embedding,
                "veracity_score": veracity_score,
                "confidence": confidence,
                "emotional_disposition": emotional_disposition,
                "evidence_memory_ids": [],
                "relation_edges": []
            }

            user_beliefs.append(belief_doc)

        # 为该用户的信念建立关系（在内存中）
        if len(user_belief_ids) > 1:
            relations = self._build_belief_relations_in_memory(user_beliefs)
            user_relations.extend(relations)

        logger.debug(f"为用户 {user_id} (特质分数: {trait_score:.2f}) 构建了 {len(user_beliefs)} 个信念和 {len(user_relations)} 个关系")

        return {"beliefs": user_beliefs, "relations": user_relations}

    def _build_belief_relations_in_memory(self, user_beliefs: List[Dict]) -> List[Dict]:
        """在内存中为用户的信念建立关系，不直接写入数据库"""
        relations = []

        # 为每个信念建立与其他信念的关系
        for i, belief1 in enumerate(user_beliefs):
            for j, belief2 in enumerate(user_beliefs):
                if i == j:
                    continue

                # 尝试使用AI服务确定两个信念之间的关系
                try:
                    relation_info = None
                    try:
                        # 尝试使用AI服务判断关系
                        relation_info = self.ai_service.determine_detailed_belief_relationship(
                            belief1["belief_summary"], belief2["belief_summary"])
                        logger.debug(f"成功获取信念关系: {relation_info}")

                        # 检查AI服务返回的数据类型
                        if not isinstance(relation_info, dict):
                            logger.warning(f"AI服务返回了非字典类型: {type(relation_info)}, 使用随机值")
                            raise ValueError("AI服务返回格式错误")

                    except Exception as e:
                        logger.warning(f"获取信念关系失败，使用随机值: {e}")
                        # AI服务失败时的备选方案
                        relation_category = random.choice(["支持", "反驳", "无关"])
                        if relation_category == "支持":
                            relation_type = random.choice(["蕴含/前提", "因果支持", "证据支持", "相关"])
                            weight = round(random.uniform(0.5, 1.0), 2)
                        elif relation_category == "反驳":
                            relation_type = random.choice(["排斥", "削弱", "反证"])
                            weight = round(random.uniform(0.5, 1.0), 2)
                        else: # 无关
                            relation_type = None
                            weight = 0.0

                        relation_info = {
                            "relation_category": relation_category,
                            "relation_type": relation_type,
                            "weight": weight,
                            "confidence": round(random.uniform(0.5, 1.0), 2),
                            "explanation": "随机生成的关系"
                        }

                    # 确保relation_info是字典类型
                    if not isinstance(relation_info, dict):
                        logger.warning(f"relation_info不是字典类型，跳过: {relation_info}")
                        continue

                    # 如果关系为"无关"或类型为None，则不创建关系边
                    if relation_info.get("relation_category") == "无关" or not relation_info.get("relation_type"):
                        logger.debug(f"信念 {belief1['node_id']} 和 {belief2['node_id']} 无关，跳过创建关系。")
                        continue

                    # 确保使用正确的字段名
                    relation_type = relation_info.get("relation_type")
                    relation_strength = relation_info.get("weight")

                    # 验证必要字段
                    if not relation_type or relation_strength is None:
                        logger.warning(f"关系信息不完整，跳过: {relation_info}")
                        continue

                    # 创建关系边
                    new_edge = {
                        "target_node_id": belief2["node_id"],
                        "relation_type": relation_type,
                        "relation_strength": relation_strength
                    }

                    # 将关系添加到belief1的relation_edges中
                    if "relation_edges" not in belief1:
                        belief1["relation_edges"] = []
                    belief1["relation_edges"].append(new_edge)

                    # 记录关系更新，用于后续批量上传
                    relations.append({
                        "belief_id": belief1["node_id"],
                        "relation_edges": belief1["relation_edges"]
                    })

                except Exception as e:
                    logger.error(f"为信念 {belief1['node_id']} 和 {belief2['node_id']} 建立关系失败: {e}")

        return relations

    def _batch_upload_beliefs(self):
        """批量上传待处理的信念数据到数据库"""
        with self._lock:
            if not self._pending_beliefs:
                return

            beliefs_to_upload = self._pending_beliefs.copy()
            relations_to_upload = self._pending_relations.copy()
            self._pending_beliefs.clear()
            self._pending_relations.clear()

        if not beliefs_to_upload:
            return

        logger.info(f"开始批量上传 {len(beliefs_to_upload)} 个信念到数据库...")

        # 准备批量操作
        bulk_operations = []

        for belief_doc in beliefs_to_upload:
            bulk_operations.append({
                "index": {
                    "_index": BELIEFS_INDEX,
                    "_id": belief_doc["node_id"]
                }
            })
            bulk_operations.append(belief_doc)

        # 执行批量上传
        try:
            if bulk_operations:
                response = ES_CLIENT.bulk(body=bulk_operations)

                # 检查批量操作结果
                errors = []
                successful_count = 0

                for item in response["items"]:
                    if "index" in item:
                        if item["index"]["status"] in [200, 201]:
                            successful_count += 1
                        else:
                            errors.append(item["index"])

                logger.info(f"信念批量上传完成 - 成功: {successful_count} 个, 失败: {len(errors)} 个")

                if errors:
                    logger.warning(f"有 {len(errors)} 个信念上传失败")
                    for error in errors[:5]:  # 只显示前5个错误
                        logger.warning(f"错误详情: {error}")

        except Exception as e:
            logger.error(f"批量上传信念失败: {e}")
            # 如果批量操作失败，回退到逐个添加
            logger.info("回退到逐个上传信念...")
            successful_count = 0
            for belief_doc in beliefs_to_upload:
                try:
                    ES_CLIENT.index(index=BELIEFS_INDEX, id=belief_doc["node_id"], document=belief_doc)
                    successful_count += 1
                except Exception as individual_error:
                    logger.error(f"上传信念 {belief_doc['node_id']} 失败: {individual_error}")

            logger.info(f"逐个上传完成 - 成功: {successful_count} 个")

        # 批量更新关系
        if relations_to_upload:
            self._batch_update_relations(relations_to_upload)

    def _batch_update_relations(self, relations_to_upload: List[Dict]):
        """批量更新信念关系"""
        logger.info(f"开始批量更新 {len(relations_to_upload)} 个信念关系...")

        # 准备批量更新操作
        bulk_operations = []

        for relation_update in relations_to_upload:
            bulk_operations.append({
                "update": {
                    "_index": BELIEFS_INDEX,
                    "_id": relation_update["belief_id"]
                }
            })
            bulk_operations.append({
                "doc": {"relation_edges": relation_update["relation_edges"]}
            })

        # 执行批量更新
        try:
            if bulk_operations:
                response = ES_CLIENT.bulk(body=bulk_operations)

                # 检查批量操作结果
                errors = []
                successful_count = 0

                for item in response["items"]:
                    if "update" in item:
                        if item["update"]["status"] in [200, 201]:
                            successful_count += 1
                        else:
                            errors.append(item["update"])

                logger.info(f"关系批量更新完成 - 成功: {successful_count} 个, 失败: {len(errors)} 个")

                if errors:
                    logger.warning(f"有 {len(errors)} 个关系更新失败")
                    for error in errors[:5]:  # 只显示前5个错误
                        logger.warning(f"错误详情: {error}")

        except Exception as e:
            logger.error(f"批量更新关系失败: {e}")
            # 如果批量操作失败，回退到逐个更新
            logger.info("回退到逐个更新关系...")
            successful_count = 0
            for relation_update in relations_to_upload:
                try:
                    ES_CLIENT.update(
                        index=BELIEFS_INDEX,
                        id=relation_update["belief_id"],
                        doc={"relation_edges": relation_update["relation_edges"]}
                    )
                    successful_count += 1
                except Exception as individual_error:
                    logger.error(f"更新信念 {relation_update['belief_id']} 关系失败: {individual_error}")

            logger.info(f"逐个更新完成 - 成功: {successful_count} 个")

    def _get_ai_data_for_belief(self, belief_text: str) -> tuple:
        """使用AI服务为信念文本生成情感倾向和嵌入向量"""
        # 获取情感倾向
        try:
            emotional_disposition = self.ai_service.analyze_emotional_impact(belief_text)
            logger.debug(f"成功获取信念'{belief_text[:30]}...'的情感向量")

            # 确保所有必需的情感维度都存在，如果缺失则使用默认值
            required_emotions = ["joy", "sadness", "anger", "fear", "surprise", "trust", "disgust", "anticipation"]
            for emotion in required_emotions:
                if emotion not in emotional_disposition:
                    emotional_disposition[emotion] = 0.5
                # 确保值在0-1范围内
                emotional_disposition[emotion] = max(0.0, min(1.0, float(emotional_disposition[emotion])))
                emotional_disposition[emotion] = round(emotional_disposition[emotion], 2)

        except Exception as e:
            logger.error(f"AI服务获取情感向量失败: {e}")
            raise Exception(f"无法获取情感向量，AI服务调用失败: {e}")

        # 获取嵌入向量
        try:
            embedding = self.ai_service.get_embedding(belief_text)
            logger.debug(f"成功获取信念'{belief_text[:30]}...'的嵌入向量")
        except Exception as e:
            logger.error(f"AI服务获取嵌入向量失败: {e}")
            raise Exception(f"无法获取嵌入向量，AI服务调用失败: {e}")

        return emotional_disposition, embedding

    def _add_posts(self):
        """添加帖子到数据库 - 只让两个用户各发两个帖子"""
        logger.info("开始添加帖子...")

        # 随机选择两个用户作为发帖人
        all_user_ids = list(self.users.keys())
        if len(all_user_ids) < 2:
            logger.warning("用户数量不足2个，无法创建帖子")
            return

        selected_authors = random.sample(all_user_ids, 2)
        logger.info(f"选择的发帖用户: {selected_authors}")

        # 确保有足够的帖子数据
        if len(self.posts_data) < 4:
            logger.warning(f"帖子数据不足4个，只有{len(self.posts_data)}个")
            return

        # 随机选择4个帖子数据
        selected_posts = random.sample(self.posts_data, 4)

        post_id_counter = 1
        posts_per_user = 2

        # 为每个选中的用户创建2个帖子
        for user_index, author_id in enumerate(selected_authors):
            for post_index in range(posts_per_user):
                post_data_index = user_index * posts_per_user + post_index
                post_data = selected_posts[post_data_index]

                post_id = f"post_{post_id_counter}"
                post_id_counter += 1

                post_doc = {
                    "post_id": post_id,
                    "title": post_data["title"],
                    "author_id": author_id,
                    "timestamp": datetime.datetime.now(datetime.timezone.utc).isoformat(),
                    "content": post_data["content"],
                    "comments": [],
                    "followers": [],
                    "views_count": random.randint(10, 1000),
                    "comments_count": 0
                }

                # 添加到ES
                ES_CLIENT.index(index=POSTS_INDEX, id=post_id, document=post_doc)

                # 更新用户的帖子列表
                self.users[author_id]["posts"].append(post_id)
                ES_CLIENT.update(
                    index=AGENTS_INDEX,
                    id=author_id,
                    doc={"posts": self.users[author_id]["posts"]}
                )

                logger.info(f"用户 {author_id} 发布了帖子: {post_data['title']}")

            # 为每个帖子添加1-5条评论
            num_comments = random.randint(1, 5)
            for _ in range(num_comments):
                # 随机选择一个不是作者的用户发表评论
                available_users = [uid for uid in self.users.keys() if uid != author_id]
                if not available_users:
                    continue

                commenter_id = random.choice(available_users)

                # 使用AI服务生成评论内容
                try:
                    # 获取用户特质信息
                    user_traits = self.users[commenter_id]

                    # 获取评论者的一个随机信念ID
                    commenter_beliefs = self.belief_module.get_agent_beliefs(commenter_id)
                    belief_id = random.choice(commenter_beliefs).get('node_id', 'default_belief_id') if commenter_beliefs else 'default_belief_id'

                    # 尝试通过AI服务生成评论内容
                    comment_content = self.ai_service.generate_comment_content(
                        user_id=commenter_id,
                        start_belief_id=belief_id,
                        post_content=post_data["content"],
                        user_traits=user_traits
                    )
                    logger.debug("成功通过AI生成评论内容")
                except Exception as e:
                    logger.warning(f"生成评论内容失败，使用备选内容: {e}")
                    # 备选方案：使用search.json中的观点
                    if self.search_data:
                        random_search = random.choice(self.search_data)
                        comment_content = random_search["detailed_statement"]
                    else:
                        comment_content = f"这是对帖子《{post_data['title']}》的一条评论。"

                comment_id = f"comment_{post_id}_{_+1}"

                comment_doc = {
                    "comment_id": comment_id,
                    "author_id": commenter_id,
                    "post_id": post_id,
                    "parent_comment_id": None,  # 直接评论帖子
                    "timestamp": datetime.datetime.now(datetime.timezone.utc).isoformat(),
                    "content": comment_content
                }

                # 添加到ES
                ES_CLIENT.index(index=COMMENTS_INDEX, id=comment_id, document=comment_doc)

                # 更新帖子的评论列表
                post_doc["comments"].append(comment_id)
                post_doc["comments_count"] += 1

                # 更新用户的评论列表
                self.users[commenter_id]["comments"].append(comment_id)
                ES_CLIENT.update(
                    index=AGENTS_INDEX,
                    id=commenter_id,
                    doc={"comments": self.users[commenter_id]["comments"]}
                )

            # 更新帖子的评论信息
            ES_CLIENT.update(
                index=POSTS_INDEX,
                id=post_id,
                doc={
                    "comments": post_doc["comments"],
                    "comments_count": post_doc["comments_count"]
                }
            )

        logger.info(f"成功添加了 {post_id_counter-1} 个帖子 (2个用户各发2个帖子)")

    def _print_belief_graphs_for_all_users(self):
        """打印所有用户的信念图"""
        logger.info("开始打印所有用户的信念图...")

        # 创建打印工具实例
        try:
            from scripts.print_belief_graph import BeliefGraphPrinter
            printer = BeliefGraphPrinter()

            # 获取所有用户ID
            user_ids = list(self.users.keys())
            if not user_ids:
                logger.info("没有找到任何用户")
                return

            logger.info(f"找到 {len(user_ids)} 个用户")

            # 为每个用户打印信念图
            for user_id in user_ids:
                printer.print_user_belief_graph(user_id)
        except ImportError as e:
            logger.warning(f"无法导入信念图打印工具: {e}")
            logger.info("跳过信念图打印步骤")

    def print_user_statistics(self):
        """打印用户特质统计信息"""
        logger.info("=== 用户特质统计信息 ===")

        trait_scores = []
        skepticism_scores = []
        verification_thresholds = []
        emotional_volatilities = []

        for _, user_data in self.users.items():
            traits = user_data["cognitive_traits"]
            trait_scores.append(traits["trait_score"])
            skepticism_scores.append(traits["skepticism_score"])
            verification_thresholds.append(traits["verification_threshold"])
            emotional_volatilities.append(traits["emotional_volatility"])

        logger.info("特质分数分布:")
        logger.info(f"  最高: {max(trait_scores):.2f}, 最低: {min(trait_scores):.2f}")
        logger.info(f"  平均: {np.mean(trait_scores):.2f}, 中位数: {np.median(trait_scores):.2f}")
        logger.info(f"  标准差: {np.std(trait_scores):.2f}")

        logger.info("怀疑分数分布:")
        logger.info(f"  最高: {max(skepticism_scores):.2f}, 最低: {min(skepticism_scores):.2f}")
        logger.info(f"  平均: {np.mean(skepticism_scores):.2f}")

        logger.info("求证阈值分布:")
        logger.info(f"  最高: {max(verification_thresholds):.2f}, 最低: {min(verification_thresholds):.2f}")
        logger.info(f"  平均: {np.mean(verification_thresholds):.2f}")

        logger.info("情绪波动分布:")
        logger.info(f"  最高: {max(emotional_volatilities):.2f}, 最低: {min(emotional_volatilities):.2f}")
        logger.info(f"  平均: {np.mean(emotional_volatilities):.2f}")

        # 统计特质分数的分布情况（连续分布，不再使用离散分类）
        logger.info("特质分数分布统计:")
        percentiles = [10, 25, 50, 75, 90]
        for p in percentiles:
            value = np.percentile(trait_scores, p)
            logger.info(f"  {p}%分位数: {value:.2f}")

        # 统计极值用户
        very_high_trait = sum(1 for score in trait_scores if score > 0.8)
        very_low_trait = sum(1 for score in trait_scores if score < 0.3)
        logger.info(f"极高特质用户(>0.8): {very_high_trait} 个 ({very_high_trait/len(trait_scores)*100:.1f}%)")
        logger.info(f"极低特质用户(<0.3): {very_low_trait} 个 ({very_low_trait/len(trait_scores)*100:.1f}%)")


def main():
    """主函数"""
    logger.info("开始基于特质的数据库初始化")

    try:
        start_time = time.time()
        initializer = TraitBasedDatabaseInitializer()

        # 打印用户特质统计信息
        initializer.print_user_statistics()

        # 初始化数据库
        initializer.initialize_database()

        end_time = time.time()

        logger.info(f"基于特质的数据库初始化完成! 总耗时: {end_time - start_time:.2f} 秒")
        return 0
    except Exception as e:
        logger.error(f"数据库初始化失败: {e}")
        return 1

if __name__ == "__main__":
    import sys
    sys.exit(main())
